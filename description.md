### Lightning Shuffler is basically just an improved and much more advanced way to interact with playlists from YouTube. 

- Made to feel alive and dynamic, the app incorporates modern aesthetics, especially glassmorphism, and plenty of bouncy, reactive animations. 
- It uses the [Recursive](https://www.recursive.design) to allow a smoothly changing typeface that doesn't expand or contract as the weight or other properties change, allowing for a more natural, fluid, and dynamic experience when the user hovers over something.
- [Tabler Icons](https://tabler.io/icons) for a vast set of modern icons.
- Performance, speed, and snappiness are priorities, which is why Lightning got incorporated into the name.
- Built in Tauri in order to support both Windows and Linux without bloat or poor performance, while maintaining the versatility of a web app.



# Some Features Include:

**Playlist Management**:

- Add playlists via a link
- You can choose which playlist or mix to play from a dropdown menu. This shows the playlist title, thumbnail, and number of videos. From this menu,you can also refresh a playlists or mix to update it with any changes that have been made externally (on YouTube) or remove it from your stored playlists.
- You can also create mixes from a dedicated menu, which allows you to select playlist to combine together from the existing playlists you've added as well as any others you choose to add (via link).

**The Sidebar**:

- Which side of the screen the sidebar is on can be changed in the settings page, but defaults to the right side by default.
- At the top of the sidebar is a searchbar that allows you to filter through a playlist or mix's videos, either by typing in the name of a video or the creator that made it.
- Below the searchbar is the playlist's queue. You can see what videos have played by scrolling up, what video is playing, and what all the rest of the videos that will play are by scrolling down in the queue. 
- Every video in the queue shows its thumbnail, title, creator, and number that it was in the queue.

**Video Player**: 
 
- _Note: In order to avoid Youtube's iframes overriding our own mediasession, we don't embed the iframe directly and instead have to use our own player_ 
- There is an ambient mode, which makes the playing video appear to subtly bleed out past the edges of its borders. Don't worry though, the app turns off calculations, especially potentially expensive ones like this, when the app isn't visible to improve performance while the app is in the background.

**Control bar**:

- Below the video player is a floating control bar, which contains various controls, including the following:
- Shuffle Playlist button
- Previous track button
- Play/pause button
- Next track button
- Loop button. Note that this has some complex functionality:
    - When enabled via left click, it will loop the current video once it ends indefinitely.
    - When right clicked, it increments a counter. Once the video ends, it will loop the video and decrement the counter. Once the counter reaches 0, it will stop looping the video, and the next time the video ends, it will play the next video in the queue.

**System Tray**:

- The app can be minimized to the system tray, where it will continue to play in the background.
- Hovering over it shows the progress through the current video and its title, for example: 0% | Video Title
- Right clicking it allows you to pick from show app and quit app.
- Left clicking shows a menu called the quickplayer, which is shown below:
   
    ![image](Quickplayer.png "Quickplayer")

    It shows the title of the video at the top, the creator of the video just below that, a neon green seek with the current time into the video on the left side of the seek bar and the total duration of the video on the right side of the seek bar. Below the seek bar is a previous track button, a play/pause button, and a next track button.
- Double clicking the system tray icon will open the full app interface.

**Help Menu**

- Don't worry, you won't need to remember most of the specific details listed here. By pressing ctrl+h, it will bring up a help menu that explains certain non-obvious features and includes all of the keyboard shortcuts you can use in the app.
