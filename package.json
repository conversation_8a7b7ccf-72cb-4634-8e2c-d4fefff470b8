{"name": "lightning-shuffler", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tabler/icons-react": "^3.34.1", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.3.1", "@tauri-apps/plugin-store": "^2.4.0", "@tauri-apps/plugin-window": "^2.0.0-alpha.1", "@types/youtube": "^0.1.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.9.1"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "vite": "^6.0.3"}}