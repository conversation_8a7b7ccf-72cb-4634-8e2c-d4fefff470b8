{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[791294565694001944, "build_script_build", false, 5382679368505911699], [8427153362654230442, "build_script_build", false, 10714208869174274282], [16429266147849286097, "build_script_build", false, 16964031257894740454]], "local": [{"RerunIfChanged": {"output": "debug\\build\\lightning-shuffler-706d268710d60b32\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}