{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 3565943984338862993, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 5037037439208315310], [1260461579271933187, "serialize_to_javascript", false, 15321268596874154648], [1967864351173319501, "muda", false, 14214964102549943876], [2013030631243296465, "webview2_com", false, 8226497882162423652], [3331586631144870129, "getrandom", false, 9491368080139811810], [4143744114649553716, "raw_window_handle", false, 4572721544728011781], [4352886507220678900, "serde_json", false, 16955600068035328252], [4537297827336760846, "thiserror", false, 6455133098042400259], [5404511084185685755, "url", false, 2530120739158663578], [6537120525306722933, "tauri_macros", false, 7146769975990098999], [6803352382179706244, "percent_encoding", false, 3712670070327799068], [8427153362654230442, "build_script_build", false, 15543994566813026308], [9010263965687315507, "http", false, 5530258970695073684], [9293239362693504808, "glob", false, 2362350705311238852], [9689903380558560274, "serde", false, 408451429707754561], [9952368442187680820, "tauri_runtime_wry", false, 3664724535773405250], [10229185211513642314, "mime", false, 3855418565933258797], [11207653606310558077, "anyhow", false, 3259595384970629015], [11989259058781683633, "dunce", false, 6706533162972636214], [12565293087094287914, "window_vibrancy", false, 13707428416804524197], [12986574360607194341, "serde_repr", false, 7131557753294731261], [13066042571740262168, "log", false, 10490242334677180732], [13077543566650298139, "heck", false, 10264810602801001668], [14585479307175734061, "windows", false, 17688331255522542299], [16727543399706004146, "cookie", false, 7815268479261483925], [16928111194414003569, "dirs", false, 5104759800168644624], [17233053221795943287, "tauri_utils", false, 3177011909979607105], [17531218394775549125, "tokio", false, 10775982368843430234], [18010483002580779355, "tauri_runtime", false, 2025918774104100873], [18035788301859549979, "tray_icon", false, 6780069914661927864]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-a6d39348344a276f\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}