{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 705431158589226293, "deps": [[376837177317575824, "softbuffer", false, 17703008955253950392], [1825855694502115139, "tao", false, 3296712567649006751], [2013030631243296465, "webview2_com", false, 8226497882162423652], [3722963349756955755, "once_cell", false, 694654376419026606], [4143744114649553716, "raw_window_handle", false, 4572721544728011781], [5404511084185685755, "url", false, 2530120739158663578], [8558698349995473911, "wry", false, 3112788898946033481], [9010263965687315507, "http", false, 5530258970695073684], [9952368442187680820, "build_script_build", false, 7764457919415125904], [13066042571740262168, "log", false, 10490242334677180732], [14585479307175734061, "windows", false, 17688331255522542299], [17233053221795943287, "tauri_utils", false, 3177011909979607105], [18010483002580779355, "tauri_runtime", false, 2025918774104100873]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-56b014db2b7d4413\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}