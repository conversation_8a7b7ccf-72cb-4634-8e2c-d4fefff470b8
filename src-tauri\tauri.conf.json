{"$schema": "https://schema.tauri.app/config/2", "productName": "lightning-shuffler", "version": "0.1.0", "identifier": "com.lightning-shuffler.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON> Shuffler", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false, "center": true}], "security": {"csp": "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https://www.youtube.com https://www.googleapis.com https://fonts.googleapis.com https://fonts.gstatic.com; img-src 'self' data: https: http:; media-src 'self' https: http:; connect-src 'self' https: http: ws: wss:;"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}