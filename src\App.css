@tailwind base;
@tailwind components;
@tailwind utilities;


/* Lightning Shuffler - Modern Glassmorphism UI */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Color Palette */
  --primary-bg: #0a0a0a;
  --secondary-bg: rgba(20, 20, 20, 0.8);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --accent-color: #00ff88;
  --accent-hover: #00cc6a;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #808080;

  /* Glassmorphism Effects */
  --glass-blur: blur(20px);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glass-border-radius: 16px;

  /* Animations */
  --transition-fast: 0.2s ease-out;
  --transition-medium: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;

  /* Typography */
  --font-family: 'Recursive', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
  font-family: var(--font-family);
  background: var(--primary-bg);
  color: var(--text-primary);
  overflow: hidden;
  font-variation-settings: 'wght' 400, 'slnt' 0, 'CASL' 0.5, 'MONO' 0;
}

#root {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Glassmorphism Base Classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--glass-border-radius);
  box-shadow: var(--glass-shadow);
}

.glass-dark {
  background: var(--secondary-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--glass-border-radius);
  box-shadow: var(--glass-shadow);
}

/* Interactive Elements */
.interactive {
  transition: all var(--transition-medium);
  cursor: pointer;
}

.interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  font-variation-settings: 'wght' 500, 'slnt' 0, 'CASL' 0.5, 'MONO' 0;
}

.interactive:active {
  transform: translateY(0);
  transition: all var(--transition-fast);
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-family: var(--font-family);
  font-size: 14px;
  font-variation-settings: 'wght' 500, 'slnt' 0, 'CASL' 0.5, 'MONO' 0;
  cursor: pointer;
  transition: all var(--transition-medium);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: var(--accent-color);
  color: var(--primary-bg);
}

.btn-primary:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 255, 136, 0.3);
}

.btn-glass {
  background: var(--glass-bg);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow);
}

/* Utility Classes */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

.text-accent {
  color: var(--accent-color);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.gap-6 {
  gap: 24px;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.flex-1 {
  flex: 1;
}

.p-2 {
  padding: 8px;
}

.p-4 {
  padding: 16px;
}

.p-6 {
  padding: 24px;
}

.m-2 {
  margin: 8px;
}

.m-4 {
  margin: 16px;
}

.rounded {
  border-radius: 8px;
}

.rounded-lg {
  border-radius: 12px;
}

.rounded-xl {
  border-radius: 16px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  transition: background var(--transition-medium);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Slider Styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: transparent;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  border: 2px solid var(--primary-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-fast);
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 255, 136, 0.3);
}

.slider::-moz-range-track {
  background: transparent;
  border: none;
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  border: 2px solid var(--primary-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-fast);
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 255, 136, 0.3);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation keyframes */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Focus styles */
input:focus,
button:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Disabled styles */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  transform: none;
  box-shadow: none;
}