import React, { useState } from 'react';
import { PlaybackState, LoopMode } from '../types';
import { formatDuration } from '../utils/youtube';
import {
  IconPlayerPlay,
  IconPlayerPause,
  IconPlayerSkipBack,
  IconPlayerSkipForward,
  IconArrowsShuffle,
  IconRepeat,
  IconRepeatOnce,
  IconVolume,
  IconVolumeOff
} from '@tabler/icons-react';

interface ControlBarProps {
  playbackState: PlaybackState;
  onPlay: () => void;
  onPause: () => void;
  onNext: () => void;
  onPrevious: () => void;
  onShuffle: () => void;
  onLoop: (mode: LoopMode, count?: number) => void;
  onSeek: (time: number) => void;
  onVolumeChange: (volume: number) => void;
}

const ControlBar: React.FC<ControlBarProps> = ({
  playbackState,
  onPlay,
  onPause,
  onNext,
  onPrevious,
  onShuffle,
  onLoop,
  onSeek,
  onVolumeChange
}) => {
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [loopClickCount, setLoopClickCount] = useState(0);

  const handlePlayPause = () => {
    if (playbackState.isPlaying) {
      onPause();
    } else {
      onPlay();
    }
  };

  const handleSeekBarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = parseFloat(e.target.value);
    onSeek(time);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const volume = parseFloat(e.target.value);
    onVolumeChange(volume);
  };

  const handleLoopClick = (e: React.MouseEvent) => {
    if (e.button === 2) { // Right click
      e.preventDefault();
      const newCount = loopClickCount + 1;
      setLoopClickCount(newCount);
      onLoop(LoopMode.Single, newCount);
    } else { // Left click
      if (playbackState.loopMode === LoopMode.None) {
        onLoop(LoopMode.Single);
      } else {
        onLoop(LoopMode.None);
        setLoopClickCount(0);
      }
    }
  };

  const getLoopIcon = () => {
    if (playbackState.loopMode === LoopMode.Single) {
      return <IconRepeatOnce className="text-accent" size={20} />;
    }
    return <IconRepeat className={playbackState.loopMode === LoopMode.Playlist ? 'text-accent' : 'text-secondary'} size={20} />;
  };

  const progress = playbackState.duration > 0 
    ? (playbackState.currentTime / playbackState.duration) * 100 
    : 0;

  return (
    <div className="glass-dark p-4 border-t border-glass-border">
      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex items-center gap-3 text-sm text-secondary mb-2">
          <span className="w-12 text-right">
            {formatDuration(playbackState.currentTime)}
          </span>
          <div className="flex-1 relative">
            <input
              type="range"
              min="0"
              max={playbackState.duration || 0}
              value={playbackState.currentTime}
              onChange={handleSeekBarChange}
              className="w-full h-2 bg-secondary-bg rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, var(--accent-color) 0%, var(--accent-color) ${progress}%, var(--secondary-bg) ${progress}%, var(--secondary-bg) 100%)`
              }}
            />
          </div>
          <span className="w-12">
            {formatDuration(playbackState.duration)}
          </span>
        </div>
      </div>

      {/* Control Buttons */}
      <div className="flex items-center justify-center gap-4">
        {/* Shuffle */}
        <button
          className={`p-3 rounded-lg transition-all hover:bg-glass-bg ${
            playbackState.isShuffled ? 'text-accent' : 'text-secondary hover:text-primary'
          }`}
          onClick={onShuffle}
          title="Shuffle playlist"
        >
          <IconArrowsShuffle size={20} />
        </button>

        {/* Previous */}
        <button
          className="p-3 rounded-lg text-secondary hover:text-primary hover:bg-glass-bg transition-all"
          onClick={onPrevious}
          title="Previous track"
        >
          <IconPlayerSkipBack size={20} />
        </button>

        {/* Play/Pause */}
        <button
          className="p-4 rounded-full bg-accent hover:bg-accent-hover text-primary-bg transition-all transform hover:scale-105 active:scale-95"
          onClick={handlePlayPause}
          title={playbackState.isPlaying ? 'Pause' : 'Play'}
        >
          {playbackState.isPlaying ? (
            <IconPlayerPause size={24} />
          ) : (
            <IconPlayerPlay size={24} />
          )}
        </button>

        {/* Next */}
        <button
          className="p-3 rounded-lg text-secondary hover:text-primary hover:bg-glass-bg transition-all"
          onClick={onNext}
          title="Next track"
        >
          <IconPlayerSkipForward size={20} />
        </button>

        {/* Loop */}
        <button
          className="p-3 rounded-lg transition-all hover:bg-glass-bg relative"
          onClick={handleLoopClick}
          onContextMenu={handleLoopClick}
          title={`Loop mode: ${playbackState.loopMode}${playbackState.loopCount > 0 ? ` (${playbackState.loopCount} times)` : ''}`}
        >
          {getLoopIcon()}
          {playbackState.loopCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-accent text-primary-bg text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {playbackState.loopCount}
            </span>
          )}
        </button>

        {/* Volume */}
        <div 
          className="relative"
          onMouseEnter={() => setShowVolumeSlider(true)}
          onMouseLeave={() => setShowVolumeSlider(false)}
        >
          <button
            className="p-3 rounded-lg text-secondary hover:text-primary hover:bg-glass-bg transition-all"
            onClick={() => onVolumeChange(playbackState.isMuted ? playbackState.volume : 0)}
            title={playbackState.isMuted ? 'Unmute' : 'Mute'}
          >
            {playbackState.isMuted || playbackState.volume === 0 ? (
              <IconVolumeOff size={20} />
            ) : (
              <IconVolume size={20} />
            )}
          </button>

          {/* Volume Slider */}
          {showVolumeSlider && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 glass-dark rounded-lg">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={playbackState.isMuted ? 0 : playbackState.volume}
                onChange={handleVolumeChange}
                className="w-20 h-2 bg-secondary-bg rounded-lg appearance-none cursor-pointer slider vertical-slider"
                style={{
                  background: `linear-gradient(to right, var(--accent-color) 0%, var(--accent-color) ${(playbackState.isMuted ? 0 : playbackState.volume) * 100}%, var(--secondary-bg) ${(playbackState.isMuted ? 0 : playbackState.volume) * 100}%, var(--secondary-bg) 100%)`
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ControlBar;
