import React, { useEffect, useRef, useState } from 'react';
import { Video } from '../types';
import { fetchMuxedStream } from '../utils/piped';

interface Props {
  video: Video | null;
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  isMuted: boolean;
  ambientMode: boolean;
  onReady: () => void;
  onPlay: () => void;
  onPause: () => void;
  onEnded: () => void;
  onTimeUpdate: (time: number) => void;
  onDurationChange: (duration: number) => void;
  onError: (error: string) => void;
}

const CustomVideoPlayer: React.FC<Props> = ({
  video,
  isPlaying,
  currentTime,
  volume,
  isMuted,
  ambientMode,
  onReady,
  onPlay,
  onPause,
  onEnded,
  onTimeUpdate,
  onDurationChange,
  onError,
}) => {
  const videoEl = useRef<HTMLVideoElement>(null);
  const [isReady, setIsReady] = useState(false);
  const [srcUrl, setSrcUrl] = useState<string | null>(null);

  // Resolve stream when video changes
  useEffect(() => {
    let isCancelled = false;
    async function load() {
      if (!video) return;
      setIsReady(false);
      setSrcUrl(null);
      const res = await fetchMuxedStream(video.id);
      if (!isCancelled) {
        if (res?.url) {
          setSrcUrl(res.url);
          // duration from source if available
          if (res.duration) onDurationChange(res.duration);
        } else {
          onError('Unable to load stream for this video');
        }
      }
    }
    load();
    return () => { isCancelled = true; };
  }, [video]);

  // Apply src and play/pause when ready
  useEffect(() => {
    const el = videoEl.current;
    if (!el) return;
    if (srcUrl) {
      el.src = srcUrl;
      const onCanPlay = () => {
        setIsReady(true);
        onReady();
        if (isPlaying) el.play().catch(() => { });
      };
      el.addEventListener('canplay', onCanPlay, { once: true });
      const onErrorLocal = () => onErrorProp('Media error');
      const onErrorProp = (msg: string) => onError(msg);
      el.addEventListener('error', onErrorLocal);
      return () => {
        el.removeEventListener('canplay', onCanPlay);
        el.removeEventListener('error', onErrorLocal);
      };
    }
  }, [srcUrl]);

  // Sync play/pause
  useEffect(() => {
    const el = videoEl.current;
    if (!el || !isReady) return;
    if (isPlaying) el.play().catch(() => { });
    else el.pause();
  }, [isPlaying, isReady]);

  // Sync volume/mute
  useEffect(() => {
    const el = videoEl.current;
    if (!el) return;
    el.muted = isMuted;
    el.volume = Math.max(0, Math.min(1, volume));
  }, [volume, isMuted]);

  // Sync time (seek if off by >2s)
  useEffect(() => {
    const el = videoEl.current;
    if (!el || !isReady) return;
    const diff = Math.abs((el.currentTime || 0) - currentTime);
    if (diff > 2) el.currentTime = currentTime;
  }, [currentTime, isReady]);

  // Events → up to app
  useEffect(() => {
    const el = videoEl.current;
    if (!el) return;
    const handleTime = () => onTimeUpdate(el.currentTime || 0);
    const handleEnded = () => onEnded();
    const handlePlay = () => onPlay();
    const handlePause = () => onPause();
    const handleLoaded = () => onDurationChange(el.duration || 0);

    el.addEventListener('timeupdate', handleTime);
    el.addEventListener('ended', handleEnded);
    el.addEventListener('play', handlePlay);
    el.addEventListener('pause', handlePause);
    el.addEventListener('loadedmetadata', handleLoaded);

    return () => {
      el.removeEventListener('timeupdate', handleTime);
      el.removeEventListener('ended', handleEnded);
      el.removeEventListener('play', handlePlay);
      el.removeEventListener('pause', handlePause);
      el.removeEventListener('loadedmetadata', handleLoaded);
    };
  }, [onTimeUpdate, onEnded, onPlay, onPause, onDurationChange]);

  // Media Session (basic)
  useEffect(() => {
    if ('mediaSession' in navigator && video) {
      navigator.mediaSession.metadata = new (window as any).MediaMetadata({
        title: video.title,
        artist: video.creator,
        artwork: [{ src: video.thumbnail, sizes: '512x512', type: 'image/png' }],
      });
    }
  }, [video]);

  if (!video) return null;

  return (
    <div className="relative w-full max-w-6xl mx-auto">
      {ambientMode && (
        <div
          className="absolute inset-0 -m-8 opacity-30 blur-3xl scale-110 -z-10"
          style={{ backgroundImage: `url(${video.thumbnail})`, backgroundSize: 'cover', backgroundPosition: 'center', backgroundRepeat: 'no-repeat' }}
        />
      )}

      <div className="relative aspect-video bg-black rounded-xl overflow-hidden shadow-2xl">
        <video ref={videoEl} className="w-full h-full" playsInline controls={false} />
      </div>

      <div className="mt-4 text-center">
        <h2 className="text-xl font-bold text-primary mb-1 line-clamp-2">{video.title}</h2>
        <p className="text-secondary">{video.creator}</p>
      </div>
    </div>
  );
};

export default CustomVideoPlayer;

