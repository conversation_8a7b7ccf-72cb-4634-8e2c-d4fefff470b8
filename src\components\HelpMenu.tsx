import React from 'react';
import { IconX, IconKeyboard, IconMouse, IconBolt } from '@tabler/icons-react';

interface HelpMenuProps {
  onClose: () => void;
}

const HelpMenu: React.FC<HelpMenuProps> = ({ onClose }) => {
  const keyboardShortcuts = [
    { key: 'Space', description: 'Play/Pause' },
    { key: 'Ctrl + H', description: 'Show/Hide Help Menu' },
    { key: 'Escape', description: 'Close Help Menu' },
    { key: '←', description: 'Seek backward 10 seconds' },
    { key: '→', description: 'Seek forward 10 seconds' },
    { key: 'M', description: 'Mute/Unmute' },
    { key: 'F', description: 'Toggle Fullscreen' },
    { key: 'S', description: 'Toggle Shuffle' },
    { key: 'L', description: 'Toggle Loop Mode' },
    { key: 'N', description: 'Next Track' },
    { key: 'P', description: 'Previous Track' },
  ];

  const features = [
    {
      title: 'Loop Functionality',
      description: 'Left click the loop button to loop the current video indefinitely. Right click to set a specific number of loops - the counter will decrease each time the video ends.'
    },
    {
      title: 'Ambient Mode',
      description: 'When enabled, the currently playing video will subtly bleed out past its borders, creating an immersive ambient effect.'
    },
    {
      title: 'Performance Optimization',
      description: 'The app automatically disables expensive visual effects like ambient mode when the window is not visible to save system resources.'
    },
    {
      title: 'System Tray Integration',
      description: 'Minimize to system tray to keep playing music in the background. Hover over the tray icon to see progress, right-click for options, left-click for quick controls.'
    },
    {
      title: 'Playlist Management',
      description: 'Add YouTube playlists by URL, refresh them to get updates, or create custom mixes by combining multiple playlists.'
    },
    {
      title: 'Search & Filter',
      description: 'Use the sidebar search to quickly find videos by title or creator name within your current playlist or mix.'
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="glass-dark rounded-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-glass-border">
          <div className="flex items-center gap-3">
            <IconBolt className="text-accent" size={24} />
            <h2 className="text-2xl font-bold text-primary">Lightning Shuffler Help</h2>
          </div>
          <button
            className="p-2 rounded-lg text-secondary hover:text-primary hover:bg-glass-bg transition-all"
            onClick={onClose}
          >
            <IconX size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Keyboard Shortcuts */}
            <div>
              <div className="flex items-center gap-2 mb-4">
                <IconKeyboard className="text-accent" size={20} />
                <h3 className="text-lg font-semibold text-primary">Keyboard Shortcuts</h3>
              </div>
              
              <div className="space-y-2">
                {keyboardShortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-glass-bg transition-colors">
                    <span className="text-secondary">{shortcut.description}</span>
                    <kbd className="px-2 py-1 bg-secondary-bg border border-glass-border rounded text-xs font-mono text-primary">
                      {shortcut.key}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>

            {/* Features & Tips */}
            <div>
              <div className="flex items-center gap-2 mb-4">
                <IconMouse className="text-accent" size={20} />
                <h3 className="text-lg font-semibold text-primary">Features & Tips</h3>
              </div>
              
              <div className="space-y-4">
                {features.map((feature, index) => (
                  <div key={index} className="p-3 rounded-lg hover:bg-glass-bg transition-colors">
                    <h4 className="font-medium text-primary mb-2">{feature.title}</h4>
                    <p className="text-sm text-secondary leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="mt-8 pt-6 border-t border-glass-border">
            <h3 className="text-lg font-semibold text-primary mb-4">About Lightning Shuffler</h3>
            <div className="grid md:grid-cols-2 gap-6 text-sm text-secondary">
              <div>
                <h4 className="font-medium text-primary mb-2">Design Philosophy</h4>
                <p className="leading-relaxed">
                  Lightning Shuffler is built with modern aesthetics in mind, featuring glassmorphism effects 
                  and smooth animations. The Recursive font provides fluid weight changes for a dynamic experience.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-primary mb-2">Performance</h4>
                <p className="leading-relaxed">
                  Built with Tauri for native performance while maintaining web app flexibility. 
                  Optimizations ensure smooth playback and minimal resource usage.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-primary mb-2">YouTube Integration</h4>
                <p className="leading-relaxed">
                  Uses the YouTube IFrame Player API for reliable playback while maintaining 
                  control over the media session for system integration.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-primary mb-2">Cross-Platform</h4>
                <p className="leading-relaxed">
                  Supports both Windows and Linux with native system tray integration 
                  and platform-specific optimizations.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-glass-border text-center">
          <p className="text-sm text-muted">
            Press <kbd className="px-1 py-0.5 bg-secondary-bg border border-glass-border rounded text-xs">Ctrl+H</kbd> anytime to open this help menu
          </p>
        </div>
      </div>
    </div>
  );
};

export default HelpMenu;
