import React, { useState, useEffect } from "react";
import { useAppStore } from "../stores/AppStore";
import PlaylistDropdown from "./PlaylistDropdown";
import VideoPlayer from "./VideoPlayer";
import ControlBar from "./ControlBar";
import Sidebar from "./Sidebar";
import HelpMenu from "./HelpMenu";
import { IconBolt } from '@tabler/icons-react';
import { addPlaylistFromUrl, refreshPlaylist, generateQueue, getNextVideo, getPreviousVideo } from '../utils/playlistManager';
import { LoopMode } from '../types';

const MainLayout: React.FC = () => {
  const { state, dispatch } = useAppStore();
  const [showHelp, setShowHelp] = useState(false);
  const [isAppVisible, setIsAppVisible] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newPlaylistUrl, setNewPlaylistUrl] = useState('');

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+H for help menu
      if (event.ctrlKey && event.key === 'h') {
        event.preventDefault();
        setShowHelp(true);
      }

      // Escape to close help menu
      if (event.key === 'Escape' && showHelp) {
        setShowHelp(false);
      }

      // Space for play/pause
      if (event.code === 'Space' && !event.ctrlKey && !event.altKey) {
        event.preventDefault();
        dispatch({
          type: 'SET_PLAYING',
          payload: !state.playbackState.isPlaying
        });
      }

      // Arrow keys for seeking
      if (event.key === 'ArrowLeft') {
        const newTime = Math.max(0, state.playbackState.currentTime - 10);
        dispatch({ type: 'SET_CURRENT_TIME', payload: newTime });
      }

      if (event.key === 'ArrowRight') {
        const newTime = Math.min(
          state.playbackState.duration,
          state.playbackState.currentTime + 10
        );
        dispatch({ type: 'SET_CURRENT_TIME', payload: newTime });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showHelp, state.playbackState, dispatch]);

  // Handle window visibility for performance optimization
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsAppVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  const sidebarOnLeft = state.settings.sidebarPosition === 'left';

  return (
    <div className="w-full h-full flex">
      {/* Sidebar - Left */}
      {sidebarOnLeft && (
        <Sidebar
          queue={state.playbackState.queue}
          currentIndex={state.playbackState.currentIndex}
          onVideoSelect={(index) => {
            const video = state.playbackState.queue[index];
            if (video) {
              dispatch({
                type: 'SET_CURRENT_VIDEO',
                payload: { video, index }
              });
            }
          }}
        />
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="glass-dark p-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <IconBolt className="text-accent" size={24} />
              <h1 className="text-xl font-bold text-primary">
                Lightning Shuffler
              </h1>
            </div>

            <PlaylistDropdown
              playlists={state.playlists}
              mixes={state.mixes}
              currentSelection={state.currentPlaylist}
              onSelect={(item) => {
                dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: item });
                // Generate queue from selected playlist/mix
                const queue = item ? item.videos : [];
                dispatch({ type: 'SET_QUEUE', payload: queue });

                // Start playing first video if queue is not empty
                if (queue.length > 0) {
                  dispatch({
                    type: 'SET_CURRENT_VIDEO',
                    payload: { video: queue[0], index: 0 }
                  });
                }
              }}
              onRefresh={async (playlist) => {
                dispatch({ type: 'SET_LOADING', payload: true });
                try {
                  const updated = await refreshPlaylist(playlist);
                  dispatch({ type: 'UPDATE_PLAYLIST', payload: updated });

                  if (state.currentPlaylist && 'url' in state.currentPlaylist && state.currentPlaylist.id === playlist.id) {
                    dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: updated });
                    dispatch({ type: 'SET_QUEUE', payload: updated.videos });
                    if (updated.videos.length > 0) {
                      dispatch({ type: 'SET_CURRENT_VIDEO', payload: { video: updated.videos[0], index: 0 } });
                    }
                  }
                } catch (error) {
                  dispatch({
                    type: 'SET_ERROR',
                    payload: error instanceof Error ? error.message : 'Failed to refresh playlist'
                  });
                } finally {
                  dispatch({ type: 'SET_LOADING', payload: false });
                }
              }}
              onRemove={(item) => {
                if ('videos' in item && 'url' in item) {
                  // It's a playlist
                  dispatch({ type: 'REMOVE_PLAYLIST', payload: item.id });
                } else {
                  // It's a mix
                  dispatch({ type: 'REMOVE_MIX', payload: item.id });
                }
              }}
              onAddPlaylist={async (url) => {
                dispatch({ type: 'SET_LOADING', payload: true });
                try {
                  const playlist = await addPlaylistFromUrl(url);
                  if (playlist) {
                    dispatch({ type: 'ADD_PLAYLIST', payload: playlist });
                    dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: playlist });
                    dispatch({ type: 'SET_QUEUE', payload: playlist.videos });
                    if (playlist.videos.length > 0) {
                      dispatch({ type: 'SET_CURRENT_VIDEO', payload: { video: playlist.videos[0], index: 0 } });
                      dispatch({ type: 'SET_PLAYING', payload: true });
                    }
                  }
                } catch (error) {
                  dispatch({
                    type: 'SET_ERROR',
                    payload: error instanceof Error ? error.message : 'Failed to add playlist'
                  });
                } finally {
                  dispatch({ type: 'SET_LOADING', payload: false });
                }
              }}
            />
          </div>

          <div className="flex items-center gap-2">
            <button
              className="btn btn-glass"
              onClick={() => setShowHelp(true)}
              title="Help (Ctrl+H)"
            >
              Help
            </button>
          </div>
        </header>

        {/* Video Player Area */}
        <div className="flex-1 flex items-center justify-center p-6">
          {state.playbackState.currentVideo ? (
            <VideoPlayer
              video={state.playbackState.currentVideo}
              isPlaying={state.playbackState.isPlaying}
              currentTime={state.playbackState.currentTime}
              volume={state.playbackState.volume}
              isMuted={state.playbackState.isMuted}
              ambientMode={state.settings.ambientMode && isAppVisible}
              onReady={() => {
                console.log('Video player ready');
              }}
              onPlay={() => {
                dispatch({ type: 'SET_PLAYING', payload: true });
              }}
              onPause={() => {
                dispatch({ type: 'SET_PLAYING', payload: false });
              }}
              onEnded={() => {
                const { playbackState } = state;

                // Handle loop-single with countdown
                if (playbackState.loopMode === 'single') {
                  if (playbackState.loopCount > 0) {
                    const newCount = playbackState.loopCount - 1;
                    // If we just consumed the last counted loop, switch to no loop for next end
                    if (newCount === 0) {
                      dispatch({ type: 'SET_LOOP_MODE', payload: { mode: LoopMode.None, count: 0 } });
                    } else {
                      dispatch({ type: 'SET_LOOP_MODE', payload: { mode: LoopMode.Single, count: newCount } });
                    }
                    // Restart current video
                    dispatch({ type: 'SET_CURRENT_TIME', payload: 0 });
                    dispatch({ type: 'SET_PLAYING', payload: true });
                    return;
                  }

                  // Indefinite single-loop
                  dispatch({ type: 'SET_CURRENT_TIME', payload: 0 });
                  dispatch({ type: 'SET_PLAYING', payload: true });
                  return;
                }

                // Otherwise advance to next based on loop mode
                const next = getNextVideo(
                  playbackState.queue,
                  playbackState.currentIndex,
                  playbackState.loopMode
                );

                if (next.index !== -1 && next.video) {
                  dispatch({ type: 'SET_CURRENT_VIDEO', payload: { video: next.video, index: next.index } });
                  dispatch({ type: 'SET_PLAYING', payload: true });
                } else {
                  // End of queue
                  dispatch({ type: 'SET_PLAYING', payload: false });
                }
              }}
              onTimeUpdate={(time) => {
                dispatch({ type: 'SET_CURRENT_TIME', payload: time });
              }}
              onDurationChange={(duration) => {
                dispatch({ type: 'SET_DURATION', payload: duration });
              }}
              onError={(error) => {
                dispatch({ type: 'SET_ERROR', payload: error });
              }}
            />
          ) : (
            <div className="glass text-center p-12 rounded-xl">
              <IconBolt className="text-accent mx-auto mb-4" size={48} />
              <h2 className="text-2xl font-bold mb-2">Welcome to Lightning Shuffler</h2>
              <p className="text-secondary mb-6">
                Select a playlist or create a mix to start playing music
              </p>
              <button
                className="btn btn-primary"
                onClick={() => setShowAddDialog(true)}
              >
                Add Playlist
              </button>

              {showAddDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                  <div className="glass-dark p-6 rounded-xl w-96 max-w-full mx-4">
                    <h3 className="text-lg font-bold text-primary mb-4">Add Playlist</h3>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-secondary mb-2">
                        YouTube Playlist URL
                      </label>
                      <input
                        type="url"
                        value={newPlaylistUrl}
                        onChange={(e) => setNewPlaylistUrl(e.target.value)}
                        placeholder="https://www.youtube.com/playlist?list=..."
                        className="w-full px-3 py-2 bg-secondary-bg border border-glass-border rounded-lg text-primary placeholder-muted focus:outline-none focus:border-accent"
                        autoFocus
                      />
                    </div>

                    <div className="flex gap-3 justify-end">
                      <button
                        className="btn btn-glass"
                        onClick={() => { setShowAddDialog(false); setNewPlaylistUrl(''); }}
                      >
                        Cancel
                      </button>
                      <button
                        className="btn btn-primary"
                        disabled={!newPlaylistUrl.trim()}
                        onClick={async () => {
                          if (!newPlaylistUrl.trim()) return;
                          dispatch({ type: 'SET_LOADING', payload: true });
                          try {
                            const playlist = await addPlaylistFromUrl(newPlaylistUrl.trim());
                            if (playlist) {
                              dispatch({ type: 'ADD_PLAYLIST', payload: playlist });
                              dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: playlist });
                              dispatch({ type: 'SET_QUEUE', payload: playlist.videos });
                              if (playlist.videos.length > 0) {
                                dispatch({ type: 'SET_CURRENT_VIDEO', payload: { video: playlist.videos[0], index: 0 } });
                                dispatch({ type: 'SET_PLAYING', payload: true });
                              }
                            }
                            setNewPlaylistUrl('');
                            setShowAddDialog(false);
                          } catch (error) {
                            dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to add playlist' });
                          } finally {
                            dispatch({ type: 'SET_LOADING', payload: false });
                          }
                        }}
                      >
                        Add Playlist
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Control Bar */}
        <ControlBar
          playbackState={state.playbackState}
          onPlay={() => dispatch({ type: 'SET_PLAYING', payload: true })}
          onPause={() => dispatch({ type: 'SET_PLAYING', payload: false })}
          onNext={() => {
            const next = getNextVideo(
              state.playbackState.queue,
              state.playbackState.currentIndex,
              state.playbackState.loopMode
            );
            if (next.index !== -1 && next.video) {
              dispatch({ type: 'SET_CURRENT_VIDEO', payload: { video: next.video, index: next.index } });
              dispatch({ type: 'SET_PLAYING', payload: true });
            }
          }}
          onPrevious={() => {
            const prev = getPreviousVideo(
              state.playbackState.queue,
              state.playbackState.currentIndex
            );
            if (prev.index !== -1 && prev.video) {
              dispatch({ type: 'SET_CURRENT_VIDEO', payload: { video: prev.video, index: prev.index } });
              dispatch({ type: 'SET_PLAYING', payload: true });
            }
          }}
          onShuffle={() => {
            const newShuffled = !state.playbackState.isShuffled;
            dispatch({ type: 'SET_SHUFFLED', payload: newShuffled });

            const source = state.currentPlaylist;
            const currentVideo = state.playbackState.currentVideo;
            if (source && currentVideo) {
              const startIndex = source.videos.findIndex(v => v.id === currentVideo.id);
              const newQueue = generateQueue(source, newShuffled, Math.max(0, startIndex));
              dispatch({ type: 'SET_QUEUE', payload: newQueue });
              dispatch({ type: 'SET_CURRENT_VIDEO', payload: { video: newQueue[0], index: 0 } });
            }
          }}
          onLoop={(mode, count) => {
            dispatch({
              type: 'SET_LOOP_MODE',
              payload: { mode, count }
            });
          }}
          onSeek={(time) => {
            dispatch({ type: 'SET_CURRENT_TIME', payload: time });
          }}
          onVolumeChange={(volume) => {
            dispatch({ type: 'SET_VOLUME', payload: volume });
          }}
        />
      </div>

      {/* Sidebar - Right */}
      {!sidebarOnLeft && (
        <Sidebar
          queue={state.playbackState.queue}
          currentIndex={state.playbackState.currentIndex}
          onVideoSelect={(index) => {
            const video = state.playbackState.queue[index];
            if (video) {
              dispatch({
                type: 'SET_CURRENT_VIDEO',
                payload: { video, index }
              });
            }
          }}
        />
      )}

      {/* Help Menu Modal */}
      {showHelp && (
        <HelpMenu onClose={() => setShowHelp(false)} />
      )}

      {/* Loading Overlay */}
      {state.isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="glass p-6 rounded-xl text-center">
            <div className="animate-spin w-8 h-8 border-2 border-accent border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-primary">Loading...</p>
          </div>
        </div>
      )}

      {/* Error Toast */}
      {state.error && (
        <div className="fixed top-4 right-4 glass-dark p-4 rounded-lg border-l-4 border-red-500 z-50">
          <div className="flex items-center justify-between">
            <p className="text-red-400">{state.error}</p>
            <button
              className="ml-4 text-red-400 hover:text-red-300"
              onClick={() => dispatch({ type: 'SET_ERROR', payload: null })}
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MainLayout;
