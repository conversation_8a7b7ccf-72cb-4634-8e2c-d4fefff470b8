import React, { useState, useRef, useEffect } from 'react';
import { Playlist, Mix, PlaylistOrMix } from '../types';
import {
  IconChevronDown,
  IconPlaylist,
  IconBlocks,
  IconRefresh,
  IconTrash,
  IconPlus,
  IconMusic
} from '@tabler/icons-react';

interface PlaylistDropdownProps {
  playlists: Playlist[];
  mixes: Mix[];
  currentSelection: PlaylistOrMix | null;
  onSelect: (item: PlaylistOrMix) => void;
  onRefresh: (playlist: Playlist) => void;
  onRemove: (item: PlaylistOrMix) => void;
}

const PlaylistDropdown: React.FC<PlaylistDropdownProps> = ({
  playlists,
  mixes,
  currentSelection,
  onSelect,
  onRefresh,
  onRemove
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newPlaylistUrl, setNewPlaylistUrl] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleItemClick = (item: PlaylistOrMix) => {
    onSelect(item);
    setIsOpen(false);
  };

  const handleRefresh = (e: React.MouseEvent, playlist: Playlist) => {
    e.stopPropagation();
    onRefresh(playlist);
  };

  const handleRemove = (e: React.MouseEvent, item: PlaylistOrMix) => {
    e.stopPropagation();
    const itemName = isPlaylist(item) ? item.title : item.name;
    if (confirm(`Are you sure you want to remove "${itemName}"?`)) {
      onRemove(item);
    }
  };

  const handleAddPlaylist = async () => {
    if (!newPlaylistUrl.trim()) return;

    try {
      // TODO: Implement add playlist functionality
      console.log('Adding playlist:', newPlaylistUrl);
      setNewPlaylistUrl('');
      setShowAddDialog(false);
    } catch (error) {
      console.error('Error adding playlist:', error);
    }
  };

  const isPlaylist = (item: PlaylistOrMix): item is Playlist => {
    return 'url' in item;
  };

  const getDisplayTitle = () => {
    if (!currentSelection) return 'Select Playlist';
    return isPlaylist(currentSelection) ? currentSelection.title : currentSelection.name;
  };

  const getDisplayInfo = () => {
    if (!currentSelection) return '';
    const videoCount = currentSelection.videos.length;
    return `${videoCount} video${videoCount !== 1 ? 's' : ''}`;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Button */}
      <button
        className="glass interactive flex items-center gap-3 px-4 py-3 rounded-lg min-w-64"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="w-12 h-12 rounded-lg overflow-hidden bg-secondary-bg flex items-center justify-center">
          {currentSelection ? (
            isPlaylist(currentSelection) ? (
              <img
                src={currentSelection.thumbnail || '/placeholder-thumbnail.jpg'}
                alt="Thumbnail"
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder-thumbnail.jpg';
                }}
              />
            ) : (
              <IconBlocks className="text-accent" size={20} />
            )
          ) : (
            <IconMusic className="text-muted" size={20} />
          )}
        </div>

        <div className="flex-1 text-left">
          <div className="font-medium text-primary truncate">
            {getDisplayTitle()}
          </div>
          <div className="text-sm text-secondary">
            {getDisplayInfo()}
          </div>
        </div>

        <IconChevronDown
          className={`text-secondary transition-transform ${isOpen ? 'rotate-180' : ''}`}
          size={20}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 glass-dark rounded-lg border border-glass-border shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Add Playlist Button */}
          <button
            className="w-full flex items-center gap-3 px-4 py-3 hover:bg-glass-bg transition-colors border-b border-glass-border"
            onClick={() => {
              setShowAddDialog(true);
              setIsOpen(false);
            }}
          >
            <IconPlus className="text-accent" size={20} />
            <span className="text-primary font-medium">Add Playlist</span>
          </button>

          {/* Playlists Section */}
          {playlists.length > 0 && (
            <>
              <div className="px-4 py-2 text-xs font-medium text-muted uppercase tracking-wide border-b border-glass-border">
                Playlists
              </div>
              {playlists.map((playlist) => (
                <div
                  key={playlist.id}
                  className="flex items-center gap-3 px-4 py-3 hover:bg-glass-bg transition-colors group cursor-pointer"
                  onClick={() => handleItemClick(playlist)}
                >
                  <div className="w-10 h-10 rounded-lg overflow-hidden bg-secondary-bg flex items-center justify-center">
                    <img
                      src={playlist.thumbnail || '/placeholder-thumbnail.jpg'}
                      alt="Thumbnail"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-thumbnail.jpg';
                      }}
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-primary truncate">
                      {playlist.title}
                    </div>
                    <div className="text-sm text-secondary">
                      {playlist.videoCount} videos
                    </div>
                  </div>

                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      className="p-1 hover:bg-glass-bg rounded text-secondary hover:text-primary"
                      onClick={(e) => handleRefresh(e, playlist)}
                      title="Refresh playlist"
                    >
                      <IconRefresh size={16} />
                    </button>
                    <button
                      className="p-1 hover:bg-glass-bg rounded text-secondary hover:text-red-400"
                      onClick={(e) => handleRemove(e, playlist)}
                      title="Remove playlist"
                    >
                      <IconTrash size={16} />
                    </button>
                  </div>

                  <IconPlaylist className="text-muted" size={16} />
                </div>
              ))}
            </>
          )}

          {/* Mixes Section */}
          {mixes.length > 0 && (
            <>
              <div className="px-4 py-2 text-xs font-medium text-muted uppercase tracking-wide border-b border-glass-border">
                Mixes
              </div>
              {mixes.map((mix) => (
                <div
                  key={mix.id}
                  className="flex items-center gap-3 px-4 py-3 hover:bg-glass-bg transition-colors group cursor-pointer"
                  onClick={() => handleItemClick(mix)}
                >
                  <div className="w-10 h-10 rounded-lg bg-accent bg-opacity-20 flex items-center justify-center">
                    <IconBlocks className="text-accent" size={20} />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-primary truncate">
                      {mix.name}
                    </div>
                    <div className="text-sm text-secondary">
                      {mix.videos.length} videos from {mix.playlists.length} playlists
                    </div>
                  </div>

                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      className="p-1 hover:bg-glass-bg rounded text-secondary hover:text-red-400"
                      onClick={(e) => handleRemove(e, mix)}
                      title="Remove mix"
                    >
                      <IconTrash size={16} />
                    </button>
                  </div>

                  <IconBlocks className="text-muted" size={16} />
                </div>
              ))}
            </>
          )}

          {/* Empty State */}
          {playlists.length === 0 && mixes.length === 0 && (
            <div className="px-4 py-8 text-center text-secondary">
              <IconMusic className="mx-auto mb-2 text-muted" size={32} />
              <p>No playlists or mixes yet</p>
              <p className="text-sm">Add a playlist to get started</p>
            </div>
          )}
        </div>
      )}

      {/* Add Playlist Dialog */}
      {showAddDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="glass-dark p-6 rounded-xl w-96 max-w-full mx-4">
            <h3 className="text-lg font-bold text-primary mb-4">Add Playlist</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-secondary mb-2">
                YouTube Playlist URL
              </label>
              <input
                type="url"
                value={newPlaylistUrl}
                onChange={(e) => setNewPlaylistUrl(e.target.value)}
                placeholder="https://www.youtube.com/playlist?list=..."
                className="w-full px-3 py-2 bg-secondary-bg border border-glass-border rounded-lg text-primary placeholder-muted focus:outline-none focus:border-accent"
                autoFocus
              />
            </div>

            <div className="flex gap-3 justify-end">
              <button
                className="btn btn-glass"
                onClick={() => {
                  setShowAddDialog(false);
                  setNewPlaylistUrl('');
                }}
              >
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={handleAddPlaylist}
                disabled={!newPlaylistUrl.trim()}
              >
                Add Playlist
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaylistDropdown;
