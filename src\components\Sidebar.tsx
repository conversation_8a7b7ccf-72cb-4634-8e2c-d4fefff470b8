import React, { useState, useMemo } from 'react';
import { Video } from '../types';
import { searchVideos, formatDuration } from '../utils/youtube';
import { IconSearch, IconMusic, IconPlayerPlay } from '@tabler/icons-react';

interface SidebarProps {
  queue: Video[];
  currentIndex: number;
  onVideoSelect: (index: number) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  queue,
  currentIndex,
  onVideoSelect
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter videos based on search query
  const filteredVideos = useMemo(() => {
    return searchVideos(queue, searchQuery);
  }, [queue, searchQuery]);

  // Get the original index of a filtered video
  const getOriginalIndex = (filteredVideo: Video): number => {
    return queue.findIndex(video => video.id === filteredVideo.id);
  };

  const handleVideoClick = (video: Video) => {
    const originalIndex = getOriginalIndex(video);
    if (originalIndex !== -1) {
      onVideoSelect(originalIndex);
    }
  };

  return (
    <div className="w-80 glass-dark flex flex-col h-full">
      {/* Search Bar */}
      <div className="p-4 border-b border-glass-border">
        <div className="relative">
          <IconSearch
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted"
            size={18}
          />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search videos or creators..."
            className="w-full pl-10 pr-4 py-2 bg-secondary-bg border border-glass-border rounded-lg text-primary placeholder-muted focus:outline-none focus:border-accent transition-colors"
          />
        </div>
      </div>

      {/* Queue Header */}
      <div className="px-4 py-3 border-b border-glass-border">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-primary">Queue</h3>
          <span className="text-sm text-secondary">
            {filteredVideos.length} of {queue.length} videos
          </span>
        </div>
      </div>

      {/* Video Queue */}
      <div className="flex-1 overflow-y-auto">
        {filteredVideos.length > 0 ? (
          <div className="space-y-1 p-2">
            {filteredVideos.map((video) => {
              const originalIndex = getOriginalIndex(video);
              const isCurrentVideo = originalIndex === currentIndex;
              const hasPlayed = originalIndex < currentIndex;

              return (
                <div
                  key={video.id}
                  className={`
                    group flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all
                    ${isCurrentVideo
                      ? 'bg-accent bg-opacity-20 border border-accent border-opacity-30'
                      : 'hover:bg-glass-bg'
                    }
                    ${hasPlayed ? 'opacity-60' : ''}
                  `}
                  onClick={() => handleVideoClick(video)}
                >
                  {/* Position Number */}
                  <div className="w-8 h-8 flex items-center justify-center text-xs font-medium rounded bg-secondary-bg">
                    {isCurrentVideo ? (
                      <IconPlayerPlay className="text-accent" size={14} />
                    ) : (
                      <span className={isCurrentVideo ? 'text-accent' : 'text-muted'}>
                        {originalIndex + 1}
                      </span>
                    )}
                  </div>

                  {/* Thumbnail */}
                  <div className="w-16 h-12 rounded overflow-hidden bg-secondary-bg flex-shrink-0">
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-thumbnail.jpg';
                      }}
                    />
                  </div>

                  {/* Video Info */}
                  <div className="flex-1 min-w-0">
                    <div className={`font-medium text-sm leading-tight mb-1 line-clamp-2 ${isCurrentVideo ? 'text-accent' : 'text-primary'}`}>
                      {video.title}
                    </div>
                    <div className="text-xs text-secondary mb-1">
                      {video.creator}
                    </div>
                    <div className="text-xs text-muted">
                      {formatDuration(video.duration)}
                    </div>
                  </div>

                  {/* Play Indicator */}
                  {isCurrentVideo && (
                    <div className="flex-shrink-0">
                      <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : queue.length > 0 ? (
          // No search results
          <div className="flex flex-col items-center justify-center h-32 text-center px-4">
            <IconSearch className="text-muted mb-2" size={32} />
            <p className="text-secondary">No videos found</p>
            <p className="text-sm text-muted">Try a different search term</p>
          </div>
        ) : (
          // Empty queue
          <div className="flex flex-col items-center justify-center h-32 text-center px-4">
            <IconMusic className="text-muted mb-2" size={32} />
            <p className="text-secondary">No videos in queue</p>
            <p className="text-sm text-muted">Select a playlist to start</p>
          </div>
        )}
      </div>

      {/* Queue Stats */}
      {queue.length > 0 && (
        <div className="p-4 border-t border-glass-border">
          <div className="text-xs text-secondary space-y-1">
            <div className="flex justify-between">
              <span>Total videos:</span>
              <span>{queue.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Total duration:</span>
              <span>
                {formatDuration(queue.reduce((total, video) => total + video.duration, 0))}
              </span>
            </div>
            {currentIndex >= 0 && (
              <div className="flex justify-between">
                <span>Progress:</span>
                <span>{currentIndex + 1} / {queue.length}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
