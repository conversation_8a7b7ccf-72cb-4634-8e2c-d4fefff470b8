import React, { useEffect, useRef, useState } from 'react';
import { Video } from '../types';

interface VideoPlayerProps {
  video: Video | null;
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  isMuted: boolean;
  ambientMode: boolean;
  onReady: () => void;
  onPlay: () => void;
  onPause: () => void;
  onEnded: () => void;
  onTimeUpdate: (time: number) => void;
  onDurationChange: (duration: number) => void;
  onError: (error: string) => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  video,
  isPlaying,
  currentTime,
  volume,
  isMuted,
  ambientMode,
  onReady,
  onPlay,
  onPause,
  onEnded,
  onTimeUpdate,
  onDurationChange,
  onError
}) => {
  const playerRef = useRef<HTMLDivElement>(null);
  const youtubePlayerRef = useRef<any>(null);
  const [isPlayerReady, setIsPlayerReady] = useState(false);

  // Load YouTube IFrame API
  useEffect(() => {
    if (!window.YT) {
      const script = document.createElement('script');
      script.src = 'https://www.youtube.com/iframe_api';
      script.async = true;
      document.body.appendChild(script);

      (window as any).onYouTubeIframeAPIReady = () => {
        console.log('YouTube IFrame API ready');
        initializePlayer();
      };
    } else {
      initializePlayer();
    }

    return () => {
      if (youtubePlayerRef.current) {
        youtubePlayerRef.current.destroy();
      }
    };
  }, []);

  // Initialize YouTube player
  const initializePlayer = () => {
    if (!playerRef.current || !video) return;

    youtubePlayerRef.current = new window.YT.Player(playerRef.current, {
      height: '100%',
      width: '100%',
      videoId: video.id,
      playerVars: {
        autoplay: 0,
        controls: 0,
        disablekb: 1,
        fs: 0,
        iv_load_policy: 3,
        modestbranding: 1,
        playsinline: 1,
        rel: 0,
        showinfo: 0,
      },
      events: {
        onReady: () => {
          setIsPlayerReady(true);
          onReady();
        },
        onStateChange: (event: any) => {
          const state = event.data;
          if (state === window.YT.PlayerState.PLAYING) {
            onPlay();
          } else if (state === window.YT.PlayerState.PAUSED) {
            onPause();
          } else if (state === window.YT.PlayerState.ENDED) {
            onEnded();
          }
        },
        onError: (event: any) => {
          onError(`YouTube player error: ${event.data}`);
        }
      }
    });
  };

  // Handle video changes
  useEffect(() => {
    if (youtubePlayerRef.current && video && isPlayerReady) {
      youtubePlayerRef.current.loadVideoById(video.id);
    }
  }, [video, isPlayerReady]);

  // Handle play/pause
  useEffect(() => {
    if (!youtubePlayerRef.current || !isPlayerReady) return;

    if (isPlaying) {
      youtubePlayerRef.current.playVideo();
    } else {
      youtubePlayerRef.current.pauseVideo();
    }
  }, [isPlaying, isPlayerReady]);

  // Handle volume changes
  useEffect(() => {
    if (!youtubePlayerRef.current || !isPlayerReady) return;

    if (isMuted) {
      youtubePlayerRef.current.mute();
    } else {
      youtubePlayerRef.current.unMute();
      youtubePlayerRef.current.setVolume(volume * 100);
    }
  }, [volume, isMuted, isPlayerReady]);

  // Handle seeking
  useEffect(() => {
    if (!youtubePlayerRef.current || !isPlayerReady) return;

    const playerCurrentTime = youtubePlayerRef.current.getCurrentTime();
    const timeDiff = Math.abs(playerCurrentTime - currentTime);

    // Only seek if the difference is significant (more than 2 seconds)
    if (timeDiff > 2) {
      youtubePlayerRef.current.seekTo(currentTime, true);
    }
  }, [currentTime, isPlayerReady]);

  // Time update interval
  useEffect(() => {
    if (!isPlayerReady || !isPlaying) return;

    const interval = setInterval(() => {
      if (youtubePlayerRef.current) {
        const time = youtubePlayerRef.current.getCurrentTime();
        const duration = youtubePlayerRef.current.getDuration();

        onTimeUpdate(time);
        if (duration) {
          onDurationChange(duration);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isPlayerReady, isPlaying, onTimeUpdate, onDurationChange]);

  if (!video) {
    return null;
  }

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      {/* Ambient Background */}
      {ambientMode && (
        <div
          className="absolute inset-0 -m-8 opacity-30 blur-3xl scale-110 -z-10"
          style={{
            backgroundImage: `url(${video.thumbnail})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        />
      )}

      {/* Video Player Container */}
      <div className="relative aspect-video bg-black rounded-xl overflow-hidden shadow-2xl">
        <div
          ref={playerRef}
          className="w-full h-full"
        />

        {/* Loading Overlay */}
        {!isPlayerReady && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-center">
              <div className="animate-spin w-8 h-8 border-2 border-accent border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-white">Loading video...</p>
            </div>
          </div>
        )}
      </div>

      {/* Video Info */}
      <div className="mt-4 text-center">
        <h2 className="text-xl font-bold text-primary mb-1 line-clamp-2">
          {video.title}
        </h2>
        <p className="text-secondary">
          {video.creator}
        </p>
      </div>
    </div>
  );
};

export default VideoPlayer;
