import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { AppState, Playlist, Mix, Video, PlaybackState, LoopMode, AppSettings } from '../types';
import { updateTrayTooltip } from '../utils/tray';

// Initial state
const initialPlaybackState: PlaybackState = {
  currentVideo: null,
  queue: [],
  currentIndex: -1,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 1,
  isMuted: false,
  isShuffled: false,
  loopMode: LoopMode.None,
  loopCount: 0,
};

const initialSettings: AppSettings = {
  sidebarPosition: 'right',
  theme: 'dark',
  ambientMode: true,
  volume: 1,
  autoplay: true,
  showNotifications: true,
};

const initialState: AppState = {
  playlists: [],
  mixes: [],
  currentPlaylist: null,
  playbackState: initialPlaybackState,
  settings: initialSettings,
  systemTray: {
    isVisible: false,
    progress: 0,
    currentVideoTitle: '',
    isPlaying: false,
  },
  quickPlayer: {
    isVisible: false,
    currentVideo: null,
    progress: 0,
    isPlaying: false,
  },
  isLoading: false,
  error: null,
};

// Action types
type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_PLAYLIST'; payload: Playlist }
  | { type: 'REMOVE_PLAYLIST'; payload: string }
  | { type: 'UPDATE_PLAYLIST'; payload: Playlist }
  | { type: 'ADD_MIX'; payload: Mix }
  | { type: 'REMOVE_MIX'; payload: string }
  | { type: 'UPDATE_MIX'; payload: Mix }
  | { type: 'SET_CURRENT_PLAYLIST'; payload: Playlist | Mix | null }
  | { type: 'SET_QUEUE'; payload: Video[] }
  | { type: 'SET_CURRENT_VIDEO'; payload: { video: Video | null; index: number } }
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'SET_CURRENT_TIME'; payload: number }
  | { type: 'SET_DURATION'; payload: number }
  | { type: 'SET_VOLUME'; payload: number }
  | { type: 'SET_MUTED'; payload: boolean }
  | { type: 'SET_SHUFFLED'; payload: boolean }
  | { type: 'SET_LOOP_MODE'; payload: { mode: LoopMode; count?: number } }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<AppSettings> }
  | { type: 'UPDATE_SYSTEM_TRAY'; payload: Partial<AppState['systemTray']> }
  | { type: 'UPDATE_QUICK_PLAYER'; payload: Partial<AppState['quickPlayer']> };

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload };

    case 'ADD_PLAYLIST':
      return { ...state, playlists: [...state.playlists, action.payload] };

    case 'REMOVE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.filter(p => p.id !== action.payload)
      };

    case 'UPDATE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.map(p =>
          p.id === action.payload.id ? action.payload : p
        )
      };

    case 'ADD_MIX':
      return { ...state, mixes: [...state.mixes, action.payload] };

    case 'REMOVE_MIX':
      return {
        ...state,
        mixes: state.mixes.filter(m => m.id !== action.payload)
      };

    case 'UPDATE_MIX':
      return {
        ...state,
        mixes: state.mixes.map(m =>
          m.id === action.payload.id ? action.payload : m
        )
      };

    case 'SET_CURRENT_PLAYLIST':
      return { ...state, currentPlaylist: action.payload };

    case 'SET_QUEUE':
      return {
        ...state,
        playbackState: { ...state.playbackState, queue: action.payload }
      };

    case 'SET_CURRENT_VIDEO':
      return {
        ...state,
        playbackState: {
          ...state.playbackState,
          currentVideo: action.payload.video,
          currentIndex: action.payload.index
        }
      };

    case 'SET_PLAYING':
      return {
        ...state,
        playbackState: { ...state.playbackState, isPlaying: action.payload },
        systemTray: { ...state.systemTray, isPlaying: action.payload },
        quickPlayer: { ...state.quickPlayer, isPlaying: action.payload }
      };

    case 'SET_CURRENT_TIME':
      const progress = state.playbackState.duration > 0
        ? (action.payload / state.playbackState.duration) * 100
        : 0;
      return {
        ...state,
        playbackState: { ...state.playbackState, currentTime: action.payload },
        systemTray: { ...state.systemTray, progress },
        quickPlayer: { ...state.quickPlayer, progress }
      };

    case 'SET_DURATION':
      return {
        ...state,
        playbackState: { ...state.playbackState, duration: action.payload }
      };

    case 'SET_VOLUME':
      return {
        ...state,
        playbackState: { ...state.playbackState, volume: action.payload },
        settings: { ...state.settings, volume: action.payload }
      };

    case 'SET_MUTED':
      return {
        ...state,
        playbackState: { ...state.playbackState, isMuted: action.payload }
      };

    case 'SET_SHUFFLED':
      return {
        ...state,
        playbackState: { ...state.playbackState, isShuffled: action.payload }
      };

    case 'SET_LOOP_MODE':
      return {
        ...state,
        playbackState: {
          ...state.playbackState,
          loopMode: action.payload.mode,
          loopCount: action.payload.count || 0
        }
      };

    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload }
      };

    case 'UPDATE_SYSTEM_TRAY':
      return {
        ...state,
        systemTray: { ...state.systemTray, ...action.payload }
      };

    case 'UPDATE_QUICK_PLAYER':
      return {
        ...state,
        quickPlayer: { ...state.quickPlayer, ...action.payload }
      };

    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider component
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load initial data from storage
  useEffect(() => {
    try {
      const raw = localStorage.getItem('ls_data');
      if (raw) {
        const data = JSON.parse(raw);
        if (Array.isArray(data.playlists)) {
          (data.playlists as Playlist[]).forEach((p) => dispatch({ type: 'ADD_PLAYLIST', payload: p }));
        }
        if (Array.isArray(data.mixes)) {
          (data.mixes as Mix[]).forEach((m) => dispatch({ type: 'ADD_MIX', payload: m }));
        }
        if (data.settings) {
          dispatch({ type: 'UPDATE_SETTINGS', payload: data.settings as Partial<AppSettings> });
        }
        if (data.currentSelection && data.currentSelection.id) {
          const sel = data.currentSelection as { type: 'playlist' | 'mix'; id: string };
          if (sel.type === 'playlist') {
            const found = (data.playlists || []).find((p: Playlist) => p.id === sel.id);
            if (found) dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: found });
          } else if (sel.type === 'mix') {
            const found = (data.mixes || []).find((m: Mix) => m.id === sel.id);
            if (found) dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: found });
          }
        }
      }
    } catch (e) {
      console.warn('Failed to load persisted data', e);
    }
  }, []);

  // Save state changes to storage (persist playlists/mixes/settings + current selection)
  useEffect(() => {
    try {
      const currentSelection = state.currentPlaylist
        ? ('url' in state.currentPlaylist
          ? { type: 'playlist' as const, id: state.currentPlaylist.id }
          : { type: 'mix' as const, id: state.currentPlaylist.id })
        : null;
      const data = {
        playlists: state.playlists,
        mixes: state.mixes,
        settings: state.settings,
        currentSelection,
      };
      localStorage.setItem('ls_data', JSON.stringify(data));
    } catch (e) {
      console.warn('Failed to save persisted data', e);
    }
  }, [state.playlists, state.mixes, state.settings, state.currentPlaylist]);

  // Update system tray tooltip text with progress and current title
  useEffect(() => {
    const title = state.playbackState.currentVideo?.title || '';
    const percent = state.playbackState.duration > 0
      ? Math.round((state.playbackState.currentTime / state.playbackState.duration) * 100)
      : 0;
    const text = `${percent}% | ${title}`.trim();
    updateTrayTooltip(text).catch(() => { });
  }, [state.playbackState.currentTime, state.playbackState.duration, state.playbackState.currentVideo]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook to use the app context
export function useAppStore() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppStore must be used within an AppProvider');
  }
  return context;
}
