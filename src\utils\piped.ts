export interface PipedMuxedStream {
  url: string;
  quality: string; // e.g., 1080p60
  mimeType: string; // e.g., video/mp4; codecs="avc1.640028, mp4a.40.2"
}

export interface PipedStreamResponse {
  title: string;
  duration: number; // seconds
  thumbnailUrl: string;
  muxedStreams?: PipedMuxedStream[];
}

const DEFAULT_PIPED = 'https://piped.video';

export async function fetchMuxedStream(videoId: string, base: string = DEFAULT_PIPED): Promise<{ url: string; duration?: number } | null> {
  try {
    const res = await fetch(`${base}/streams/${encodeURIComponent(videoId)}`);
    if (!res.ok) return null;
    const data = (await res.json()) as PipedStreamResponse;
    const muxed = (data.muxedStreams || []).filter(s => s.mimeType?.includes('mp4'));
    if (!muxed.length) return null;
    // Prefer up to 1080p, fallback to highest available
    const preferredOrder = ['1080', '720', '480', '360'];
    const pick = muxed
      .slice()
      .sort((a, b) => {
        const qa = parseInt(a.quality, 10) || 0;
        const qb = parseInt(b.quality, 10) || 0;
        return qb - qa;
      })
      .find(s => preferredOrder.some(p => s.quality.includes(p))) || muxed[0];

    return { url: pick.url, duration: data.duration };
  } catch (e) {
    console.warn('Failed to resolve piped stream', e);
    return null;
  }
}

