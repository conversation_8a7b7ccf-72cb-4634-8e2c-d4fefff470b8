import { Playlist, Mix, Video } from '../types';
import { 
  extractPlaylistId, 
  fetchPlaylistInfo, 
  convertToPlaylist, 
  generateId, 
  shuffleArray 
} from './youtube';

/**
 * Add a playlist from YouTube URL
 */
export async function addPlaylistFromUrl(url: string): Promise<Playlist | null> {
  const playlistId = extractPlaylistId(url);
  if (!playlistId) {
    throw new Error('Invalid YouTube playlist URL');
  }

  try {
    const youtubePlaylist = await fetchPlaylistInfo(playlistId);
    if (!youtubePlaylist) {
      throw new Error('Playlist not found or is private');
    }

    return convertToPlaylist(youtubePlaylist, url);
  } catch (error) {
    console.error('Error adding playlist:', error);
    throw error;
  }
}

/**
 * Refresh a playlist to get latest videos
 */
export async function refreshPlaylist(playlist: Playlist): Promise<Playlist> {
  try {
    const updatedPlaylist = await addPlaylistFromUrl(playlist.url);
    if (!updatedPlaylist) {
      throw new Error('Failed to refresh playlist');
    }

    // Preserve the original ID and update timestamp
    return {
      ...updatedPlaylist,
      id: playlist.id,
      lastUpdated: new Date()
    };
  } catch (error) {
    console.error('Error refreshing playlist:', error);
    throw error;
  }
}

/**
 * Create a mix from multiple playlists
 */
export function createMix(
  name: string, 
  playlists: Playlist[], 
  selectedPlaylistIds: string[]
): Mix {
  const selectedPlaylists = playlists.filter(p => selectedPlaylistIds.includes(p.id));
  
  // Combine all videos from selected playlists
  const allVideos: Video[] = [];
  selectedPlaylists.forEach(playlist => {
    allVideos.push(...playlist.videos);
  });

  // Remove duplicates based on video ID
  const uniqueVideos = allVideos.filter((video, index, self) => 
    index === self.findIndex(v => v.id === video.id)
  );

  return {
    id: generateId(),
    name,
    playlists: selectedPlaylistIds,
    videos: uniqueVideos,
    createdAt: new Date(),
    lastModified: new Date()
  };
}

/**
 * Update a mix with new playlist selections
 */
export function updateMix(
  mix: Mix, 
  playlists: Playlist[], 
  selectedPlaylistIds: string[]
): Mix {
  const selectedPlaylists = playlists.filter(p => selectedPlaylistIds.includes(p.id));
  
  // Combine all videos from selected playlists
  const allVideos: Video[] = [];
  selectedPlaylists.forEach(playlist => {
    allVideos.push(...playlist.videos);
  });

  // Remove duplicates based on video ID
  const uniqueVideos = allVideos.filter((video, index, self) => 
    index === self.findIndex(v => v.id === video.id)
  );

  return {
    ...mix,
    playlists: selectedPlaylistIds,
    videos: uniqueVideos,
    lastModified: new Date()
  };
}

/**
 * Generate queue from playlist or mix
 */
export function generateQueue(
  source: Playlist | Mix, 
  shuffle: boolean = false,
  startFromIndex: number = 0
): Video[] {
  let queue = [...source.videos];
  
  if (shuffle) {
    // If shuffling, create a new shuffled array but keep the selected video at the start
    if (startFromIndex > 0 && startFromIndex < queue.length) {
      const selectedVideo = queue[startFromIndex];
      const otherVideos = queue.filter((_, index) => index !== startFromIndex);
      const shuffledOthers = shuffleArray(otherVideos);
      queue = [selectedVideo, ...shuffledOthers];
    } else {
      queue = shuffleArray(queue);
    }
  } else if (startFromIndex > 0) {
    // If not shuffling but starting from a specific index, reorder the queue
    queue = [...queue.slice(startFromIndex), ...queue.slice(0, startFromIndex)];
  }
  
  return queue;
}

/**
 * Get next video in queue considering loop mode
 */
export function getNextVideo(
  queue: Video[], 
  currentIndex: number, 
  loopMode: 'none' | 'single' | 'playlist'
): { video: Video | null; index: number } {
  if (queue.length === 0) {
    return { video: null, index: -1 };
  }

  switch (loopMode) {
    case 'single':
      // Stay on the same video
      return { 
        video: queue[currentIndex] || null, 
        index: currentIndex 
      };
    
    case 'playlist':
      // Move to next video, loop back to start if at end
      const nextIndex = (currentIndex + 1) % queue.length;
      return { 
        video: queue[nextIndex], 
        index: nextIndex 
      };
    
    case 'none':
    default:
      // Move to next video, stop if at end
      if (currentIndex + 1 < queue.length) {
        return { 
          video: queue[currentIndex + 1], 
          index: currentIndex + 1 
        };
      }
      return { video: null, index: -1 };
  }
}

/**
 * Get previous video in queue
 */
export function getPreviousVideo(
  queue: Video[], 
  currentIndex: number
): { video: Video | null; index: number } {
  if (queue.length === 0 || currentIndex <= 0) {
    return { video: null, index: -1 };
  }

  return { 
    video: queue[currentIndex - 1], 
    index: currentIndex - 1 
  };
}

/**
 * Validate playlist data
 */
export function validatePlaylist(playlist: any): playlist is Playlist {
  return (
    playlist &&
    typeof playlist.id === 'string' &&
    typeof playlist.title === 'string' &&
    typeof playlist.url === 'string' &&
    Array.isArray(playlist.videos) &&
    playlist.videos.every(validateVideo)
  );
}

/**
 * Validate video data
 */
export function validateVideo(video: any): video is Video {
  return (
    video &&
    typeof video.id === 'string' &&
    typeof video.title === 'string' &&
    typeof video.creator === 'string' &&
    typeof video.thumbnail === 'string' &&
    typeof video.duration === 'number' &&
    typeof video.position === 'number' &&
    typeof video.url === 'string'
  );
}

/**
 * Validate mix data
 */
export function validateMix(mix: any): mix is Mix {
  return (
    mix &&
    typeof mix.id === 'string' &&
    typeof mix.name === 'string' &&
    Array.isArray(mix.playlists) &&
    mix.playlists.every((id: any) => typeof id === 'string') &&
    Array.isArray(mix.videos) &&
    mix.videos.every(validateVideo)
  );
}

/**
 * Export playlist data for backup
 */
export function exportPlaylistData(playlists: Playlist[], mixes: Mix[]) {
  return {
    version: '1.0',
    exportDate: new Date().toISOString(),
    playlists,
    mixes
  };
}

/**
 * Import playlist data from backup
 */
export function importPlaylistData(data: any): { playlists: Playlist[]; mixes: Mix[] } {
  if (!data || typeof data !== 'object') {
    throw new Error('Invalid import data');
  }

  const playlists = Array.isArray(data.playlists) 
    ? data.playlists.filter(validatePlaylist)
    : [];

  const mixes = Array.isArray(data.mixes)
    ? data.mixes.filter(validateMix)
    : [];

  return { playlists, mixes };
}
