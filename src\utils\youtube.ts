import { Playlist, Video, YouTubePlaylistInfo, YouTubeVideoInfo } from '../types';

// YouTube URL patterns
const YOUTUBE_PLAYLIST_REGEX = /(?:youtube\.com\/(?:watch\?.*&list=|playlist\?list=)|youtu\.be\/.*\?list=)([a-zA-Z0-9_-]+)/;
const YOUTUBE_VIDEO_REGEX = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/;

// YouTube API configuration
let YOUTUBE_API_KEY: string = (typeof localStorage !== 'undefined' && localStorage.getItem('yt_api_key')) || 'AIzaSyAFGB8-5IffhA-sAtvt7MYQJLwQJZTPypI';
const YOUTUBE_API_BASE = 'https://www.googleapis.com/youtube/v3';

export function setYouTubeApiKey(key: string) {
  YOUTUBE_API_KEY = key;
  try {
    localStorage.setItem('yt_api_key', key);
  } catch (_) {
    // ignore if storage not available
  }
}

export function getYouTubeApiKey() {
  return YOUTUBE_API_KEY;
}

/**
 * Extract playlist ID from YouTube URL
 */
export function extractPlaylistId(url: string): string | null {
  const match = url.match(YOUTUBE_PLAYLIST_REGEX);
  return match ? match[1] : null;
}

/**
 * Extract video ID from YouTube URL
 */
export function extractVideoId(url: string): string | null {
  const match = url.match(YOUTUBE_VIDEO_REGEX);
  return match ? match[1] : null;
}

/**
 * Validate if URL is a YouTube playlist
 */
export function isYouTubePlaylistUrl(url: string): boolean {
  return YOUTUBE_PLAYLIST_REGEX.test(url);
}

/**
 * Validate if URL is a YouTube video
 */
export function isYouTubeVideoUrl(url: string): boolean {
  return YOUTUBE_VIDEO_REGEX.test(url);
}

/**
 * Convert ISO 8601 duration to seconds
 */
export function parseDuration(duration: string): number {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return 0;

  const hours = parseInt(match[1] || '0', 10);
  const minutes = parseInt(match[2] || '0', 10);
  const seconds = parseInt(match[3] || '0', 10);

  return hours * 3600 + minutes * 60 + seconds;
}

/**
 * Format seconds to MM:SS or HH:MM:SS
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Get video thumbnail URL
 */
export function getVideoThumbnail(videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'medium'): string {
  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
}

/**
 * Fetch playlist information from YouTube API
 */
export async function fetchPlaylistInfo(playlistId: string): Promise<YouTubePlaylistInfo | null> {
  if (!YOUTUBE_API_KEY) {
    throw new Error('YouTube API key not configured');
  }

  try {
    // Fetch playlist details
    const playlistResponse = await fetch(
      `${YOUTUBE_API_BASE}/playlists?part=snippet&id=${playlistId}&key=${YOUTUBE_API_KEY}`
    );

    if (!playlistResponse.ok) {
      throw new Error('Failed to fetch playlist information');
    }

    const playlistData = await playlistResponse.json();

    if (!playlistData.items || playlistData.items.length === 0) {
      return null;
    }

    const playlist = playlistData.items[0];

    // Fetch playlist items
    const videos = await fetchPlaylistVideos(playlistId);

    return {
      id: playlistId,
      title: playlist.snippet.title,
      thumbnail: playlist.snippet.thumbnails.medium?.url || playlist.snippet.thumbnails.default?.url || '',
      videoCount: videos.length,
      videos
    };
  } catch (error) {
    console.error('Error fetching playlist info:', error);
    return null;
  }
}

/**
 * Fetch all videos from a playlist
 */
export async function fetchPlaylistVideos(playlistId: string): Promise<YouTubeVideoInfo[]> {
  if (!YOUTUBE_API_KEY) {
    throw new Error('YouTube API key not configured');
  }

  const videos: YouTubeVideoInfo[] = [];
  let nextPageToken = '';

  try {
    do {
      const response = await fetch(
        `${YOUTUBE_API_BASE}/playlistItems?part=snippet&playlistId=${playlistId}&maxResults=50${nextPageToken ? `&pageToken=${nextPageToken}` : ''}&key=${YOUTUBE_API_KEY}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch playlist videos');
      }

      const data = await response.json();

      // Get video IDs for duration lookup
      const videoIds = data.items
        .filter((item: any) => item.snippet.resourceId.kind === 'youtube#video')
        .map((item: any) => item.snippet.resourceId.videoId);

      // Fetch video details for durations
      const videoDetails = await fetchVideoDetails(videoIds);

      // Process videos
      data.items.forEach((item: any) => {
        if (item.snippet.resourceId.kind === 'youtube#video') {
          const videoId = item.snippet.resourceId.videoId;
          const details = videoDetails.find(v => v.id === videoId);

          videos.push({
            id: videoId,
            title: item.snippet.title,
            channelTitle: item.snippet.videoOwnerChannelTitle || item.snippet.channelTitle,
            thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url || '',
            duration: details?.contentDetails?.duration || 'PT0S',
            position: item.snippet.position
          });
        }
      });

      nextPageToken = data.nextPageToken || '';
    } while (nextPageToken);

    return videos;
  } catch (error) {
    console.error('Error fetching playlist videos:', error);
    return [];
  }
}

/**
 * Fetch video details including duration
 */
export async function fetchVideoDetails(videoIds: string[]): Promise<any[]> {
  if (!YOUTUBE_API_KEY || videoIds.length === 0) {
    return [];
  }

  try {
    const response = await fetch(
      `${YOUTUBE_API_BASE}/videos?part=contentDetails&id=${videoIds.join(',')}&key=${YOUTUBE_API_KEY}`
    );

    if (!response.ok) {
      throw new Error('Failed to fetch video details');
    }

    const data = await response.json();
    return data.items || [];
  } catch (error) {
    console.error('Error fetching video details:', error);
    return [];
  }
}

/**
 * Convert YouTube API data to internal Playlist format
 */
export function convertToPlaylist(youtubePlaylist: YouTubePlaylistInfo, originalUrl: string): Playlist {
  const videos: Video[] = youtubePlaylist.videos.map(video => ({
    id: video.id,
    title: video.title,
    creator: video.channelTitle,
    thumbnail: video.thumbnail,
    duration: parseDuration(video.duration),
    position: video.position,
    url: `https://www.youtube.com/watch?v=${video.id}`
  }));

  return {
    id: youtubePlaylist.id,
    title: youtubePlaylist.title,
    thumbnail: youtubePlaylist.thumbnail,
    videos,
    url: originalUrl,
    lastUpdated: new Date(),
    videoCount: videos.length
  };
}

/**
 * Shuffle array using Fisher-Yates algorithm
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Search videos by title or creator
 */
export function searchVideos(videos: Video[], query: string): Video[] {
  if (!query.trim()) return videos;

  const searchTerm = query.toLowerCase();
  return videos.filter(video =>
    video.title.toLowerCase().includes(searchTerm) ||
    video.creator.toLowerCase().includes(searchTerm)
  );
}

/**
 * Generate a unique ID for playlists/mixes
 */
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
