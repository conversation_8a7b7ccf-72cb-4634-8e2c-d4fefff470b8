/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        'primary-bg': 'var(--primary-bg)',
        'secondary-bg': 'var(--secondary-bg)',
        'glass-bg': 'var(--glass-bg)',
        'glass-border': 'var(--glass-border)',
        accent: 'var(--accent-color)',
        'accent-hover': 'var(--accent-hover)',
        primary: 'var(--text-primary)',
        secondary: 'var(--text-secondary)',
        muted: 'var(--text-muted)'
      },
      borderColor: {
        'glass-border': 'var(--glass-border)',
        accent: 'var(--accent-color)'
      }
    }
  },
  plugins: [require('@tailwindcss/aspect-ratio')]
};
